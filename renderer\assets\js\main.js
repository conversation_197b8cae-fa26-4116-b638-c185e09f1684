// Main.js - Khởi tạo ứng dụng và điều phối các module

// Thiết lập các biến toàn cục
const electron = require("electron");
const { ipcRenderer } = electron;
// robotjs sẽ được import khi cần thiết để tránh lỗi context-aware

// Export ipcRenderer ra global scope để các module khác sử dụng
window.ipcRenderer = ipcRenderer;

let isAutomationRunning = false;
let currentScript = null;
let automationAvailable = false;

// Kiểm tra automation có khả dụng không
async function checkAutomationAvailable() {
  try {
    const result = await ipcRenderer.invoke("automation-check-available");
    automationAvailable = result.available;
    if (!automationAvailable) {
      console.error("Automation không khả dụng:", result.error);
    } else {
      console.log("Automation đã sẵn sàng");
    }
    return automationAvailable;
  } catch (error) {
    console.error("Lỗi khi kiểm tra automation:", error);
    automationAvailable = false;
    return false;
  }
}

// Hàm khởi tạo ứng dụng
async function initApp() {
  console.log("Khởi tạo ứng dụng...");

  // Kiểm tra automation có khả dụng không
  await checkAutomationAvailable();

  // Khởi tạo các tab Bootstrap
  const triggerTabList = document.querySelectorAll("#mainTabs a");
  triggerTabList.forEach((triggerEl) => {
    const tabTrigger = new bootstrap.Tab(triggerEl);
    triggerEl.addEventListener("click", (event) => {
      event.preventDefault();
      tabTrigger.show();
    });
  });

  // Khởi tạo các module
  console.log("Khởi tạo các module...");
  initAccountsModule();
  initAutomationModule();
  initSettingsModule();

  // Lắng nghe sự kiện từ main process
  setupIpcListeners();
  console.log("Ứng dụng đã được khởi tạo hoàn tất");
}

// Thiết lập lắng nghe sự kiện IPC
function setupIpcListeners() {
  // Lắng nghe sự kiện thực thi script tự động
  ipcRenderer.on("execute-automation-script", (event, script) => {
    executeAutomationScript(script);
  });
}

// Thực thi script tự động
async function executeAutomationScript(script) {
  if (isAutomationRunning) {
    console.log(
      "Một script khác đang chạy, vui lòng dừng trước khi chạy script mới"
    );
    showToast("Một script khác đang chạy", "warning");
    return;
  }

  // Kiểm tra automation có khả dụng không
  if (!automationAvailable) {
    console.error("Automation không khả dụng");
    showToast(
      "Chức năng automation không khả dụng. Vui lòng kiểm tra cài đặt.",
      "danger"
    );
    return;
  }

  // Đánh dấu đang chạy script
  isAutomationRunning = true;
  currentScript = script;

  console.log("Bắt đầu thực thi script:", script.name);
  showToast(`Đang thực thi script: ${script.name}`, "info");

  try {
    // Chờ 3 giây để người dùng chuyển focus sang cửa sổ emulator
    showToast("Chờ 3 giây để chuyển focus sang cửa sổ game...", "info");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log(`Thực hiện ${script.actions.length} hành động...`);

    // Thực hiện từng hành động trong script
    for (let i = 0; i < script.actions.length; i++) {
      if (!isAutomationRunning) {
        console.log("Script đã bị dừng.");
        showToast("Script đã bị dừng", "warning");
        break;
      }

      const action = script.actions[i];
      console.log(
        `Thực hiện hành động ${i + 1}/${script.actions.length}:`,
        action
      );
      await executeAction(action);
    }

    if (isAutomationRunning) {
      console.log("Script đã hoàn thành thành công");
      showToast("Script đã hoàn thành thành công!", "success");
    }
  } catch (error) {
    console.error("Lỗi khi thực thi script:", error);
    showToast(`Lỗi khi thực thi script: ${error.message}`, "danger");
  } finally {
    isAutomationRunning = false;
    currentScript = null;

    // Cập nhật lại trạng thái nút chạy
    if (window.updateRunButtonState) {
      window.updateRunButtonState(false);
    }
  }
}

// Thực hiện một hành động trong script
async function executeAction(action) {
  try {
    switch (action.type) {
      case "key":
        // Mô phỏng nhấn phím thông qua IPC
        await ipcRenderer.invoke("automation-execute-action", {
          type: "key",
          key: action.key,
        });
        break;
      case "text":
        // Nhập văn bản thông qua IPC
        await ipcRenderer.invoke("automation-execute-action", {
          type: "text",
          text: action.text,
        });
        break;
      case "username":
        // Lấy tài khoản hiện tại và nhập
        const currentAccount1 = await getCurrentAccount();
        if (currentAccount1 && currentAccount1.username) {
          await ipcRenderer.invoke("automation-execute-action", {
            type: "text",
            text: currentAccount1.username,
          });
        }
        break;
      case "password":
        // Lấy mật khẩu hiện tại và nhập
        const currentAccount2 = await getCurrentAccount();
        if (currentAccount2 && currentAccount2.password) {
          await ipcRenderer.invoke("automation-execute-action", {
            type: "text",
            text: currentAccount2.password,
          });
        }
        break;
      case "delay":
        // Tạm dừng thực thi (xử lý local)
        await new Promise((resolve) => setTimeout(resolve, action.duration));
        break;
    }

    // Thêm độ trễ nhỏ giữa các hành động
    await new Promise((resolve) => setTimeout(resolve, 200));
  } catch (error) {
    console.error("Lỗi khi thực hiện hành động automation:", error);
    throw error;
  }
}

// Lấy tài khoản hiện tại từ main process
async function getCurrentAccount() {
  try {
    return await ipcRenderer.invoke("automation-get-current-account");
  } catch (error) {
    console.error("Lỗi khi lấy thông tin tài khoản hiện tại:", error);
    return null;
  }
}

// Dừng script đang chạy
function stopAutomationScript() {
  isAutomationRunning = false;
}

// Khi trang đã tải xong
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM đã sẵn sàng, bắt đầu khởi tạo ứng dụng...");
  initApp();
});

// Backup: Khởi tạo khi window load (nếu DOMContentLoaded đã qua)
if (document.readyState === "loading") {
  console.log("Document đang loading, chờ DOMContentLoaded...");
} else {
  console.log("Document đã sẵn sàng, khởi tạo ngay lập tức...");
  initApp();
}

// Export các hàm cần thiết cho các module khác
window.appMain = {
  executeAutomationScript,
  stopAutomationScript,
};
