// automation.js - <PERSON><PERSON><PERSON> quản lý các kịch bản tự động hóa

// Biến toàn cục
// ipcR<PERSON>er được khai báo trong main.js
let scripts = [];
let currentScriptActions = [];
let currentScriptName = "";
let currentEditingIndex = -1;
let scriptContainer;
let scriptSortable;

// Kiểm tra và hiển thị trạng thái automation
async function checkAndDisplayAutomationStatus() {
  try {
    const result = await ipcRenderer.invoke("automation-check-available");
    const statusElement = document.getElementById("automationStatus");

    if (statusElement) {
      if (result.available) {
        statusElement.innerHTML =
          '<span class="text-success"><i class="bi bi-check-circle"></i> Automation sẵn sàng</span>';
      } else {
        statusElement.innerHTML =
          '<span class="text-danger"><i class="bi bi-x-circle"></i> Automation không khả dụng: ' +
          result.error +
          "</span>";
      }
    }
  } catch (error) {
    console.error("Lỗi khi kiểm tra trạng thái automation:", error);
  }
}

// Khởi tạo module
async function initAutomationModule() {
  // Kiểm tra trạng thái automation
  await checkAndDisplayAutomationStatus();

  // Tải danh sách kịch bản đã lưu
  loadSavedScripts();

  // Khởi tạo vùng chứa kịch bản
  scriptContainer = document.getElementById("scriptContainer");

  // Khởi tạo Sortable.js để kéo thả các hành động
  initSortable();

  // Đăng ký các sự kiện
  setupEventListeners();
}

// Thiết lập các trình lắng nghe sự kiện
function setupEventListeners() {
  // Sự kiện cho các action items
  const actionItems = document.querySelectorAll(".action-item");
  actionItems.forEach((item) => {
    item.addEventListener("click", function () {
      const actionType = this.getAttribute("data-action-type");

      switch (actionType) {
        case "key":
          const key = this.getAttribute("data-key");
          addKeyAction(key);
          break;
        case "text":
          showTextInputModal();
          break;
        case "delay":
          showDelayInputModal();
          break;
        case "username":
          addUsernameAction();
          break;
        case "password":
          addPasswordAction();
          break;
      }
    });
  });

  // Sự kiện cho các nút
  document
    .getElementById("saveScriptBtn")
    .addEventListener("click", saveCurrentScript);
  document
    .getElementById("runScriptBtn")
    .addEventListener("click", runCurrentScript);
  document
    .getElementById("stopScriptBtn")
    .addEventListener("click", stopCurrentScript);
  document
    .getElementById("clearScriptBtn")
    .addEventListener("click", clearCurrentScript);
  document
    .getElementById("templateLoginBtn")
    .addEventListener("click", loadLoginTemplate);
  document
    .getElementById("confirmTextBtn")
    .addEventListener("click", confirmTextInput);
  document
    .getElementById("confirmDelayBtn")
    .addEventListener("click", confirmDelayInput);
}

// Khởi tạo Sortable.js
function initSortable() {
  scriptSortable = new Sortable(scriptContainer, {
    animation: 150,
    handle: ".script-item-handle",
    ghostClass: "sortable-ghost",
    dragClass: "sortable-drag",
    onEnd: function () {
      // Cập nhật mảng actions dựa trên vị trí mới
      updateActionsFromDOM();
    },
  });
}

// Tải danh sách kịch bản đã lưu
function loadSavedScripts() {
  try {
    const savedScripts = localStorage.getItem("ninja_scripts");
    if (savedScripts) {
      scripts = JSON.parse(savedScripts);
      renderSavedScripts();
    }
  } catch (error) {
    console.error("Lỗi khi tải danh sách kịch bản:", error);
  }
}

// Hiển thị danh sách kịch bản đã lưu
function renderSavedScripts() {
  const savedScriptsList = document.getElementById("savedScriptsList");
  savedScriptsList.innerHTML = "";

  if (scripts.length === 0) {
    savedScriptsList.innerHTML = `
            <div class="alert alert-info">
                Chưa có kịch bản nào được lưu. Hãy tạo kịch bản mới!
            </div>
        `;
    return;
  }

  scripts.forEach((script, index) => {
    const scriptElement = document.createElement("div");
    scriptElement.className = "saved-script";
    scriptElement.innerHTML = `
            <div class="saved-script-name">
                <i class="bi bi-code-square me-2"></i>
                ${script.name} (${script.actions.length} thao tác)
            </div>
            <div class="saved-script-actions">
                <button class="btn btn-sm btn-primary load-script" data-index="${index}">
                    <i class="bi bi-arrow-clockwise"></i> Tải
                </button>
                <button class="btn btn-sm btn-success run-script" data-index="${index}">
                    <i class="bi bi-play-fill"></i> Chạy
                </button>
                <button class="btn btn-sm btn-danger delete-script" data-index="${index}">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

    savedScriptsList.appendChild(scriptElement);
  });

  // Đăng ký sự kiện cho các nút
  document.querySelectorAll(".load-script").forEach((button) => {
    button.addEventListener("click", loadScript);
  });

  document.querySelectorAll(".run-script").forEach((button) => {
    button.addEventListener("click", runSavedScript);
  });

  document.querySelectorAll(".delete-script").forEach((button) => {
    button.addEventListener("click", deleteScript);
  });
}

// Hiển thị các hành động trong kịch bản hiện tại
function renderCurrentScript() {
  scriptContainer.innerHTML = "";

  if (currentScriptActions.length === 0) {
    scriptContainer.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="bi bi-arrow-left-circle-fill fs-3"></i>
                <p class="mt-2">Kéo các thao tác từ menu bên trái để tạo kịch bản</p>
            </div>
        `;
    return;
  }

  currentScriptActions.forEach((action, index) => {
    const actionElement = createActionElement(action, index);
    scriptContainer.appendChild(actionElement);
  });

  // Đăng ký sự kiện cho các nút
  document.querySelectorAll(".edit-action").forEach((button) => {
    button.addEventListener("click", editAction);
  });

  document.querySelectorAll(".delete-action").forEach((button) => {
    button.addEventListener("click", deleteAction);
  });
}

// Tạo phần tử HTML cho một hành động
function createActionElement(action, index) {
  const actionElement = document.createElement("div");
  actionElement.className = `script-item ${action.type}-action`;
  actionElement.setAttribute("data-action-index", index);

  let actionIcon, actionText;

  switch (action.type) {
    case "key":
      actionIcon = getKeyIcon(action.key);
      actionText = getKeyText(action.key);
      break;
    case "text":
      actionIcon = "bi bi-input-cursor-text";
      actionText = `Nhập văn bản: "${action.text}"`;
      break;
    case "delay":
      actionIcon = "bi bi-hourglass-split";
      actionText = `Chờ ${action.duration}ms`;
      break;
    case "username":
      actionIcon = "bi bi-person-badge";
      actionText = "Nhập tên tài khoản";
      break;
    case "password":
      actionIcon = "bi bi-key";
      actionText = "Nhập mật khẩu";
      break;
  }

  actionElement.innerHTML = `
        <div class="script-item-handle">
            <i class="bi bi-grip-vertical"></i>
        </div>
        <div class="script-item-content">
            <div class="script-item-icon">
                <i class="${actionIcon}"></i>
            </div>
            <div class="script-item-text">
                ${actionText}
            </div>
        </div>
        <div class="script-item-actions">
            <button class="btn btn-sm btn-outline-secondary edit-action" data-index="${index}">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-action" data-index="${index}">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    `;

  return actionElement;
}

// Lấy biểu tượng cho phím
function getKeyIcon(key) {
  switch (key) {
    case "up":
      return "bi bi-arrow-up-circle";
    case "down":
      return "bi bi-arrow-down-circle";
    case "left":
      return "bi bi-arrow-left-circle";
    case "right":
      return "bi bi-arrow-right-circle";
    case "enter":
      return "bi bi-arrow-return-left";
    case "backspace":
      return "bi bi-backspace";
    case "f2":
      return "bi bi-keyboard";
    default:
      return "bi bi-keyboard";
  }
}

// Lấy text mô tả cho phím
function getKeyText(key) {
  switch (key) {
    case "up":
      return "Nhấn phím mũi tên lên";
    case "down":
      return "Nhấn phím mũi tên xuống";
    case "left":
      return "Nhấn phím mũi tên trái";
    case "right":
      return "Nhấn phím mũi tên phải";
    case "enter":
      return "Nhấn phím Enter";
    case "backspace":
      return "Nhấn phím Backspace";
    case "f2":
      return "Nhấn phím F2";
    default:
      return `Nhấn phím ${key}`;
  }
}

// Thêm hành động phím
function addKeyAction(key) {
  currentScriptActions.push({
    type: "key",
    key: key,
  });

  renderCurrentScript();
}

// Thêm hành động nhập tài khoản
function addUsernameAction() {
  currentScriptActions.push({
    type: "username",
  });

  renderCurrentScript();
}

// Thêm hành động nhập mật khẩu
function addPasswordAction() {
  currentScriptActions.push({
    type: "password",
  });

  renderCurrentScript();
}

// Hiển thị modal nhập văn bản
function showTextInputModal() {
  currentEditingIndex = -1;
  document.getElementById("textInput").value = "";
  const textModal = new bootstrap.Modal(
    document.getElementById("textInputModal")
  );
  textModal.show();
}

// Hiển thị modal nhập độ trễ
function showDelayInputModal() {
  currentEditingIndex = -1;
  document.getElementById("delayInput").value = "1000";
  const delayModal = new bootstrap.Modal(
    document.getElementById("delayInputModal")
  );
  delayModal.show();
}

// Xác nhận nhập văn bản
function confirmTextInput() {
  const text = document.getElementById("textInput").value;

  if (text) {
    if (currentEditingIndex >= 0) {
      // Chỉnh sửa hành động hiện có
      currentScriptActions[currentEditingIndex].text = text;
    } else {
      // Thêm hành động mới
      currentScriptActions.push({
        type: "text",
        text: text,
      });
    }

    renderCurrentScript();
    bootstrap.Modal.getInstance(
      document.getElementById("textInputModal")
    ).hide();
    currentEditingIndex = -1;
  }
}

// Xác nhận nhập độ trễ
function confirmDelayInput() {
  const delay = parseInt(document.getElementById("delayInput").value);

  if (!isNaN(delay) && delay >= 100) {
    if (currentEditingIndex >= 0) {
      // Chỉnh sửa hành động hiện có
      currentScriptActions[currentEditingIndex].duration = delay;
    } else {
      // Thêm hành động mới
      currentScriptActions.push({
        type: "delay",
        duration: delay,
      });
    }

    renderCurrentScript();
    bootstrap.Modal.getInstance(
      document.getElementById("delayInputModal")
    ).hide();
    currentEditingIndex = -1;
  }
}

// Chỉnh sửa hành động
function editAction(event) {
  const index = parseInt(event.currentTarget.getAttribute("data-index"));
  currentEditingIndex = index;
  const action = currentScriptActions[index];

  switch (action.type) {
    case "text":
      document.getElementById("textInput").value = action.text;
      new bootstrap.Modal(document.getElementById("textInputModal")).show();
      break;
    case "delay":
      document.getElementById("delayInput").value = action.duration;
      new bootstrap.Modal(document.getElementById("delayInputModal")).show();
      break;
  }
}

// Xóa hành động
function deleteAction(event) {
  const index = parseInt(event.currentTarget.getAttribute("data-index"));
  currentScriptActions.splice(index, 1);
  renderCurrentScript();
}

// Cập nhật mảng hành động từ DOM
function updateActionsFromDOM() {
  const newActions = [];
  const actionElements = scriptContainer.querySelectorAll(".script-item");

  actionElements.forEach((element) => {
    const index = parseInt(element.getAttribute("data-action-index"));
    newActions.push(currentScriptActions[index]);
  });

  currentScriptActions = newActions;
}

// Lưu kịch bản hiện tại
function saveCurrentScript() {
  const scriptName =
    document.getElementById("scriptName").value.trim() ||
    `Script ${scripts.length + 1}`;

  if (currentScriptActions.length === 0) {
    alert("Không thể lưu kịch bản trống. Vui lòng thêm ít nhất một hành động!");
    return;
  }

  // Tạo kịch bản mới
  const script = {
    name: scriptName,
    actions: [...currentScriptActions],
    createdAt: new Date().toISOString(),
  };

  scripts.push(script);

  // Lưu vào localStorage
  localStorage.setItem("ninja_scripts", JSON.stringify(scripts));

  // Cập nhật giao diện
  renderSavedScripts();

  // Thông báo
  showToast(`Đã lưu kịch bản "${scriptName}"`, "success");
}

// Tải kịch bản đã lưu
function loadScript(event) {
  const index = parseInt(event.currentTarget.getAttribute("data-index"));
  const script = scripts[index];

  // Cập nhật kịch bản hiện tại
  currentScriptName = script.name;
  currentScriptActions = [...script.actions];

  // Cập nhật giao diện
  document.getElementById("scriptName").value = currentScriptName;
  renderCurrentScript();
}

// Chạy kịch bản đã lưu
function runSavedScript(event) {
  const index = parseInt(event.currentTarget.getAttribute("data-index"));
  const script = scripts[index];

  // Kiểm tra xem window.appMain có tồn tại không
  if (!window.appMain || !window.appMain.executeAutomationScript) {
    console.error("window.appMain.executeAutomationScript không khả dụng");
    showToast("Lỗi hệ thống: Không thể khởi chạy automation", "danger");
    return;
  }

  console.log("Đang chạy script đã lưu:", script);

  // Cập nhật UI button
  updateRunButtonState(true);

  // Hiển thị thông báo bắt đầu
  showToast("Đang khởi chạy script automation...", "info");

  try {
    window.appMain.executeAutomationScript(script);
  } catch (error) {
    console.error("Lỗi khi chạy script:", error);
    showToast("Lỗi khi chạy script: " + error.message, "danger");
    updateRunButtonState(false);
  }
}

// Chạy kịch bản hiện tại
function runCurrentScript() {
  if (currentScriptActions.length === 0) {
    showToast(
      "Không thể chạy kịch bản trống. Vui lòng thêm ít nhất một hành động!",
      "warning"
    );
    return;
  }

  // Kiểm tra xem window.appMain có tồn tại không
  if (!window.appMain || !window.appMain.executeAutomationScript) {
    console.error("window.appMain.executeAutomationScript không khả dụng");
    showToast("Lỗi hệ thống: Không thể khởi chạy automation", "danger");
    return;
  }

  const script = {
    name:
      document.getElementById("scriptName").value.trim() || "Kịch bản tạm thời",
    actions: [...currentScriptActions],
  };

  console.log("Đang chạy script:", script);

  // Cập nhật UI button
  updateRunButtonState(true);

  // Hiển thị thông báo bắt đầu
  showToast("Đang khởi chạy script automation...", "info");

  try {
    window.appMain.executeAutomationScript(script);
  } catch (error) {
    console.error("Lỗi khi chạy script:", error);
    showToast("Lỗi khi chạy script: " + error.message, "danger");
    updateRunButtonState(false);
  }
}

// Xóa kịch bản đã lưu
function deleteScript(event) {
  if (!confirm("Bạn có chắc chắn muốn xóa kịch bản này?")) {
    return;
  }

  const index = parseInt(event.currentTarget.getAttribute("data-index"));
  scripts.splice(index, 1);

  // Lưu vào localStorage
  localStorage.setItem("ninja_scripts", JSON.stringify(scripts));

  // Cập nhật giao diện
  renderSavedScripts();
}

// Xóa kịch bản hiện tại
function clearCurrentScript() {
  if (
    currentScriptActions.length > 0 &&
    !confirm("Bạn có chắc chắn muốn xóa tất cả các thao tác?")
  ) {
    return;
  }

  currentScriptActions = [];
  renderCurrentScript();
}

// Tải mẫu kịch bản đăng nhập
function loadLoginTemplate() {
  currentScriptActions = [
    { type: "key", key: "down" },
    { type: "key", key: "down" },
    { type: "key", key: "down" },
    { type: "key", key: "enter" },
    { type: "delay", duration: 500 },
    // Xóa tên đăng nhập cũ
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    // Nhập tên đăng nhập
    { type: "username" },
    { type: "key", key: "down" },
    // Xóa mật khẩu cũ
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    { type: "key", key: "backspace" },
    // Nhập mật khẩu
    { type: "password" },
    { type: "delay", duration: 500 },
    // Nhấn F2 để đăng nhập
    { type: "key", key: "f2" },
  ];

  document.getElementById("scriptName").value = "Đăng nhập tự động";
  renderCurrentScript();
}

// Dừng script hiện tại
function stopCurrentScript() {
  if (window.appMain && window.appMain.stopAutomationScript) {
    window.appMain.stopAutomationScript();
    showToast("Đã dừng script automation", "info");
    updateRunButtonState(false);
  }
}

// Cập nhật trạng thái nút chạy
function updateRunButtonState(isRunning) {
  const runBtn = document.getElementById("runScriptBtn");
  const stopBtn = document.getElementById("stopScriptBtn");

  if (!runBtn) return;

  if (isRunning) {
    runBtn.innerHTML =
      '<i class="bi bi-hourglass-split me-1"></i> Đang chạy...';
    runBtn.classList.remove("btn-success");
    runBtn.classList.add("btn-warning");
    runBtn.disabled = true;

    if (stopBtn) {
      stopBtn.style.display = "inline-block";
    }
  } else {
    runBtn.innerHTML = '<i class="bi bi-play-fill me-1"></i> Chạy';
    runBtn.classList.remove("btn-warning");
    runBtn.classList.add("btn-success");
    runBtn.disabled = false;

    if (stopBtn) {
      stopBtn.style.display = "none";
    }
  }
}

// Export module
window.initAutomationModule = initAutomationModule;
window.updateRunButtonState = updateRunButtonState;
