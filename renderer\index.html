<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ninja Game Account Manager</title>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" href="./assets/css/style.css" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 sidebar">
          <div
            class="d-flex flex-column flex-shrink-0 p-3 text-white bg-dark h-100"
          >
            <a
              href="/"
              class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none"
            >
              <i class="bi bi-controller me-2"></i>
              <span class="fs-4">Ninja Manager</span>
            </a>
            <hr />
            <ul class="nav nav-pills flex-column mb-auto" id="mainTabs">
              <li class="nav-item">
                <a
                  href="#accounts"
                  class="nav-link active text-white"
                  data-bs-toggle="tab"
                >
                  <i class="bi bi-person-fill me-2"></i>
                  Tài khoản
                </a>
              </li>
              <li>
                <a
                  href="#automation"
                  class="nav-link text-white"
                  data-bs-toggle="tab"
                >
                  <i class="bi bi-robot me-2"></i>
                  Tự động hóa
                </a>
              </li>
              <li>
                <a
                  href="#settings"
                  class="nav-link text-white"
                  data-bs-toggle="tab"
                >
                  <i class="bi bi-gear-fill me-2"></i>
                  Cài đặt
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-10 main-content">
          <div class="tab-content">
            <!-- Accounts Tab -->
            <div class="tab-pane fade show active" id="accounts">
              <div class="p-4">
                <div
                  class="d-flex justify-content-between align-items-center mb-4"
                >
                  <h2>
                    <i class="bi bi-person-fill me-2"></i>Quản lý tài khoản
                  </h2>
                  <button class="btn btn-primary" id="addAccountBtn">
                    <i class="bi bi-plus-circle me-1"></i> Thêm tài khoản mới
                  </button>
                </div>

                <div class="card">
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hover">
                        <thead>
                          <tr>
                            <th>Tên tài khoản</th>
                            <th>Mật khẩu</th>
                            <th>Máy chủ</th>
                            <th>Ghi chú</th>
                            <th>Hành động</th>
                          </tr>
                        </thead>
                        <tbody id="accountsList"></tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Automation Tab -->
            <div class="tab-pane fade" id="automation">
              <div class="p-4">
                <div
                  class="d-flex justify-content-between align-items-center mb-3"
                >
                  <h2><i class="bi bi-robot me-2"></i>Tạo Script Tự động</h2>
                  <div id="automationStatus" class="badge bg-secondary">
                    Đang kiểm tra...
                  </div>
                </div>

                <div class="row mt-4">
                  <div class="col-md-4">
                    <div class="card mb-4">
                      <div class="card-header">
                        <h5>Thao tác có sẵn</h5>
                      </div>
                      <div class="card-body">
                        <div class="action-items" id="actionItems">
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="up"
                          >
                            <i class="bi bi-arrow-up-circle"></i> Phím lên
                          </div>
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="down"
                          >
                            <i class="bi bi-arrow-down-circle"></i> Phím xuống
                          </div>
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="left"
                          >
                            <i class="bi bi-arrow-left-circle"></i> Phím trái
                          </div>
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="right"
                          >
                            <i class="bi bi-arrow-right-circle"></i> Phím phải
                          </div>
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="enter"
                          >
                            <i class="bi bi-arrow-return-left"></i> Enter
                          </div>
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="backspace"
                          >
                            <i class="bi bi-backspace"></i> Backspace
                          </div>
                          <div
                            class="action-item"
                            data-action-type="key"
                            data-key="f2"
                          >
                            <i class="bi bi-keyboard"></i> F2
                          </div>
                          <div class="action-item" data-action-type="text">
                            <i class="bi bi-input-cursor-text"></i> Nhập văn bản
                          </div>
                          <div class="action-item" data-action-type="delay">
                            <i class="bi bi-hourglass-split"></i> Chờ
                          </div>
                          <div class="action-item" data-action-type="username">
                            <i class="bi bi-person-badge"></i> Nhập tài khoản
                          </div>
                          <div class="action-item" data-action-type="password">
                            <i class="bi bi-key"></i> Nhập mật khẩu
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="card">
                      <div class="card-header">
                        <h5>Mẫu Script</h5>
                      </div>
                      <div class="card-body">
                        <button
                          class="btn btn-outline-primary mb-2 w-100"
                          id="templateLoginBtn"
                        >
                          <i class="bi bi-door-open me-1"></i> Đăng nhập tự động
                        </button>
                        <button
                          class="btn btn-outline-primary w-100"
                          id="customTemplateBtn"
                        >
                          <i class="bi bi-plus-circle me-1"></i> Thêm mẫu mới
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-8">
                    <div class="card">
                      <div
                        class="card-header d-flex justify-content-between align-items-center"
                      >
                        <h5>Script của tôi</h5>
                        <div>
                          <button
                            class="btn btn-sm btn-primary me-1"
                            id="saveScriptBtn"
                          >
                            <i class="bi bi-save me-1"></i> Lưu
                          </button>
                          <button
                            class="btn btn-sm btn-success"
                            id="runScriptBtn"
                          >
                            <i class="bi bi-play-fill me-1"></i> Chạy
                          </button>
                          <button
                            class="btn btn-sm btn-danger"
                            id="stopScriptBtn"
                            style="display: none"
                          >
                            <i class="bi bi-stop-fill me-1"></i> Dừng
                          </button>
                        </div>
                      </div>
                      <div class="card-body">
                        <div class="script-builder mb-3">
                          <input
                            type="text"
                            class="form-control mb-2"
                            id="scriptName"
                            placeholder="Tên script"
                          />
                          <div
                            class="script-container"
                            id="scriptContainer"
                          ></div>
                        </div>
                        <div class="script-controls">
                          <button class="btn btn-danger" id="clearScriptBtn">
                            <i class="bi bi-trash me-1"></i> Xóa tất cả
                          </button>
                        </div>
                      </div>
                    </div>

                    <div class="card mt-3">
                      <div class="card-header">
                        <h5>Script đã lưu</h5>
                      </div>
                      <div class="card-body">
                        <div id="savedScriptsList"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings">
              <div class="p-4">
                <h2><i class="bi bi-gear-fill me-2"></i>Cài đặt</h2>
                <div class="card mt-4">
                  <div class="card-body">
                    <form id="settingsForm">
                      <div class="mb-3">
                        <label class="form-label">Đường dẫn Java</label>
                        <div class="input-group">
                          <input
                            type="text"
                            class="form-control"
                            id="javaBinPath"
                            placeholder="Đường dẫn đến Java"
                          />
                          <button
                            class="btn btn-outline-secondary"
                            type="button"
                            id="selectJavaBtn"
                          >
                            <i class="bi bi-folder2-open"></i>
                          </button>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label class="form-label"
                          >Đường dẫn MicroEmulator</label
                        >
                        <div class="input-group">
                          <input
                            type="text"
                            class="form-control"
                            id="emuJarPath"
                            placeholder="Đường dẫn đến file MicroEmulator.jar"
                          />
                          <button
                            class="btn btn-outline-secondary"
                            type="button"
                            id="selectEmuBtn"
                          >
                            <i class="bi bi-folder2-open"></i>
                          </button>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Đường dẫn Game JAR</label>
                        <div class="input-group">
                          <input
                            type="text"
                            class="form-control"
                            id="gameJarPath"
                            placeholder="Đường dẫn đến file game.jar"
                          />
                          <button
                            class="btn btn-outline-secondary"
                            type="button"
                            id="selectGameBtn"
                          >
                            <i class="bi bi-folder2-open"></i>
                          </button>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Thư mục profiles</label>
                        <div class="input-group">
                          <input
                            type="text"
                            class="form-control"
                            id="profilesDir"
                            placeholder="Đường dẫn đến thư mục profiles"
                          />
                          <button
                            class="btn btn-outline-secondary"
                            type="button"
                            id="selectProfilesDirBtn"
                          >
                            <i class="bi bi-folder2-open"></i>
                          </button>
                        </div>
                      </div>
                      <div class="form-check mb-3">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="autoTypeCheck"
                        />
                        <label class="form-check-label" for="autoTypeCheck">
                          Tự động gõ phím (Sử dụng với cẩn trọng)
                        </label>
                      </div>
                      <button type="submit" class="btn btn-primary">
                        Lưu cài đặt
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Account Modal -->
    <div class="modal fade" id="accountModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="accountModalLabel">
              Thêm tài khoản mới
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form id="accountForm">
              <input type="hidden" id="accountId" />
              <div class="mb-3">
                <label for="username" class="form-label">Tên tài khoản</label>
                <input
                  type="text"
                  class="form-control"
                  id="username"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="password" class="form-label">Mật khẩu</label>
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="server" class="form-label">Máy chủ</label>
                <select class="form-select" id="server" required>
                  <option value="" disabled selected>Chọn máy chủ</option>
                  <option value="1">1 - Gia tộc ninja</option>
                  <option value="2">2 - Bokken</option>
                  <option value="3">3 - Shuriken</option>
                  <option value="4">4 - Tessen</option>
                  <option value="5">5 - Kunai</option>
                  <option value="6">6 - Katana</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="note" class="form-label">Ghi chú</label>
                <textarea class="form-control" id="note" rows="2"></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Hủy
            </button>
            <button type="button" class="btn btn-primary" id="saveAccountBtn">
              Lưu
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Text Input Modal for Automation -->
    <div class="modal fade" id="textInputModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Nhập văn bản</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="textInput" class="form-label">Văn bản</label>
              <input type="text" class="form-control" id="textInput" />
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Hủy
            </button>
            <button type="button" class="btn btn-primary" id="confirmTextBtn">
              Xác nhận
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delay Input Modal -->
    <div class="modal fade" id="delayInputModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Thời gian chờ</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="delayInput" class="form-label"
                >Thời gian chờ (mili giây)</label
              >
              <input
                type="number"
                class="form-control"
                id="delayInput"
                min="100"
                value="1000"
              />
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Hủy
            </button>
            <button type="button" class="btn btn-primary" id="confirmDelayBtn">
              Xác nhận
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="./assets/js/accounts.js"></script>
    <script src="./assets/js/automation.js"></script>
    <script src="./assets/js/settings.js"></script>
    <script src="./assets/js/main.js"></script>
  </body>
</html>
