const { app, B<PERSON>erWindow, ipcMain, dialog } = require("electron");
const path = require("path");
const fs = require("fs");
const { spawn, exec } = require("child_process");
const Store = require("electron-store").default || require("electron-store"); // Đã sửa tên biến

// Import node-key-sender trong main process (thay thế RobotJS)
let keySender = null;
let keySenderError = null;
try {
  keySender = require("node-key-sender");
  console.log("node-key-sender đã được tải thành công trong main process");
} catch (error) {
  console.error(
    "Lỗi khi tải node-key-sender trong main process:",
    error.message
  );
  keySenderError = error.message;
  console.log("Ứng dụng sẽ hoạt động mà không có chức năng automation");
}

// Thiết lập lưu trữ
const store = new Store();

// Đọ<PERSON> cấu hình
let config = {};
try {
  config = JSON.parse(fs.readFileSync(path.join(__dirname, "config.json")));
} catch (error) {
  console.error("Lỗi khi đọc file config.json:", error);
  config = {
    emuType: "microemulator",
    javaBin: "C:\\Program Files\\Java\\jdk-20\\bin\\java.exe",
    emuJar: "C:\\Users\\<USER>\\Desktop\\manager_game\\pb\\MICROEMULATOR.jar",
    gameJar:
      "C:\\Users\\<USER>\\Desktop\\manager_game\\pb\\217_TrungDucTV_Premium.jar",
    profilesDir: "C:\\Users\\<USER>\\Desktop\\manager_game\\pb\\profiles",
    autoType: false,
  };
}

// Biến toàn cục để theo dõi cửa sổ chính
let mainWindow;

// Tạo cửa sổ chính
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
    icon: path.join(__dirname, "assets/icons/icon.png"),
  });

  // Load giao diện chính
  mainWindow.loadFile("renderer/index.html");

  // Mở DevTools trong chế độ phát triển
  // mainWindow.webContents.openDevTools();
}

// Khởi tạo ứng dụng
app.whenReady().then(() => {
  createWindow();

  app.on("activate", function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Thoát ứng dụng khi tắt tất cả cửa sổ
app.on("window-all-closed", function () {
  if (process.platform !== "darwin") app.quit();
});

// Quản lý tài khoản
ipcMain.handle("get-accounts", async () => {
  try {
    const accountsData = fs.readFileSync(
      path.join(__dirname, "accounts.json"),
      "utf8"
    );
    return JSON.parse(accountsData);
  } catch (error) {
    console.error("Lỗi khi đọc danh sách tài khoản:", error);
    return [];
  }
});

ipcMain.handle("save-accounts", async (event, accounts) => {
  try {
    fs.writeFileSync(
      path.join(__dirname, "accounts.json"),
      JSON.stringify(accounts, null, 2),
      "utf8"
    );
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi lưu danh sách tài khoản:", error);
    return { success: false, error: error.message };
  }
});

// Quản lý cấu hình
ipcMain.handle("get-config", async () => {
  return config;
});

ipcMain.handle("save-config", async (event, newConfig) => {
  try {
    config = newConfig;
    fs.writeFileSync(
      path.join(__dirname, "config.json"),
      JSON.stringify(config, null, 2),
      "utf8"
    );
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi lưu cấu hình:", error);
    return { success: false, error: error.message };
  }
});

// Khởi chạy game
ipcMain.handle("launch-game", async (event, account) => {
  try {
    const { javaBin, emuJar, gameJar } = config;

    if (!javaBin || !emuJar || !gameJar) {
      return {
        success: false,
        error:
          "Thiếu thông tin cấu hình để khởi chạy game. Vui lòng kiểm tra cài đặt.",
      };
    }

    // Lưu tài khoản hiện tại để tự động đăng nhập
    store.set("currentAccount", account);

    // Khởi chạy MicroEmulator với game JAR
    const emulatorProcess = spawn(javaBin, ["-jar", emuJar, gameJar], {
      detached: true,
    });

    emulatorProcess.unref();

    return { success: true };
  } catch (error) {
    console.error("Lỗi khi khởi chạy game:", error);
    return { success: false, error: error.message };
  }
});

// Xử lý tự động hóa
ipcMain.handle("execute-script", async (event, script) => {
  try {
    // Thực thi script tự động
    // Script sẽ được xử lý bởi module robotjs trong renderer
    mainWindow.webContents.send("execute-automation-script", script);
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi thực thi script:", error);
    return { success: false, error: error.message };
  }
});

// Chọn file JAR
ipcMain.handle("select-jar-file", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openFile"],
    filters: [{ name: "JAR Files", extensions: ["jar"] }],
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// Chọn thư mục
ipcMain.handle("select-directory", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openDirectory"],
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// ===== AUTOMATION IPC HANDLERS =====

// Kiểm tra node-key-sender có sẵn không
ipcMain.handle("automation-check-available", async () => {
  return {
    available: keySender !== null,
    error: keySender
      ? null
      : keySenderError || "node-key-sender không thể tải trong main process",
  };
});

// Thực hiện hành động automation
ipcMain.handle("automation-execute-action", async (event, action) => {
  if (!keySender) {
    throw new Error("node-key-sender không khả dụng");
  }

  try {
    switch (action.type) {
      case "key":
        await keySender.sendKey(action.key);
        break;
      case "text":
        await keySender.sendText(action.text);
        break;
      case "delay":
        // Delay được xử lý ở renderer side
        break;
      default:
        throw new Error(`Loại hành động không được hỗ trợ: ${action.type}`);
    }
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi thực hiện automation action:", error);
    return { success: false, error: error.message };
  }
});

// Lấy thông tin tài khoản hiện tại cho automation
ipcMain.handle("automation-get-current-account", async () => {
  try {
    // Đọc từ file current_account.json nếu có
    const currentAccountPath = path.join(__dirname, "current_account.json");
    if (fs.existsSync(currentAccountPath)) {
      const accountData = fs.readFileSync(currentAccountPath, "utf8");
      return JSON.parse(accountData);
    }
    return null;
  } catch (error) {
    console.error("Lỗi khi lấy thông tin tài khoản hiện tại:", error);
    return null;
  }
});
