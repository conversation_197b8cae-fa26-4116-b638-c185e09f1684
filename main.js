const { app, <PERSON>rowserWindow, ipc<PERSON>ain, dialog } = require("electron");
const path = require("path");
const fs = require("fs");
const { spawn, exec } = require("child_process");
const Store = require("electron-store").default || require("electron-store"); // Đã sửa tên biến

// Biến để theo dõi trạng thái automation
let automationAvailable = false;
let automationError = null;

// Hàm custom để gửi phím sử dụng Java path từ cài đặt
async function sendKeyWithCustomJava(keyCode) {
  return new Promise((resolve, reject) => {
    if (!config.javaBin) {
      reject(new Error("Java path chưa được cấu hình trong cài đặt"));
      return;
    }

    const jarPath = path.join(
      __dirname,
      "node_modules/node-key-sender/jar/key-sender.jar"
    );
    const command = `"${config.javaBin}" -jar "${jarPath}" ${keyCode}`;

    exec(command, {}, (error, stdout, stderr) => {
      if (error == null) {
        resolve(stdout);
      } else {
        reject(error);
      }
    });
  });
}

// Hàm custom để gửi text sử dụng Java path từ cài đặt
async function sendTextWithCustomJava(text) {
  return new Promise((resolve, reject) => {
    if (!config.javaBin) {
      reject(new Error("Java path chưa được cấu hình trong cài đặt"));
      return;
    }

    const jarPath = path.join(
      __dirname,
      "node_modules/node-key-sender/jar/key-sender.jar"
    );
    const keyCodes = [];

    for (let i = 0; i < text.length; i++) {
      keyCodes.push(text[i]);
    }

    const command = `"${config.javaBin}" -jar "${jarPath}" ${keyCodes.join(
      " "
    )}`;

    exec(command, {}, (error, stdout, stderr) => {
      if (error == null) {
        resolve(stdout);
      } else {
        reject(error);
      }
    });
  });
}

// Hàm kiểm tra automation có khả dụng không
function checkAutomationAvailability() {
  if (!config.javaBin) {
    automationAvailable = false;
    automationError = "Java path chưa được cấu hình trong cài đặt";
    return;
  }

  // Kiểm tra file JAR có tồn tại không
  const jarPath = path.join(
    __dirname,
    "node_modules/node-key-sender/jar/key-sender.jar"
  );
  if (!fs.existsSync(jarPath)) {
    automationAvailable = false;
    automationError = "Không tìm thấy key-sender.jar";
    return;
  }

  automationAvailable = true;
  automationError = null;
  console.log("Automation đã sẵn sàng với Java path:", config.javaBin);
}

// Thiết lập lưu trữ
const store = new Store();

// Đọc cấu hình
let config = {};
try {
  config = JSON.parse(fs.readFileSync(path.join(__dirname, "config.json")));
} catch (error) {
  console.error("Lỗi khi đọc file config.json:", error);
  config = {
    emuType: "microemulator",
    javaBin: "C:\\Program Files\\Java\\jdk-20\\bin\\java.exe",
    emuJar: "C:\\Users\\<USER>\\Desktop\\manager_game\\pb\\MICROEMULATOR.jar",
    gameJar:
      "C:\\Users\\<USER>\\Desktop\\manager_game\\pb\\217_TrungDucTV_Premium.jar",
    profilesDir: "C:\\Users\\<USER>\\Desktop\\manager_game\\pb\\profiles",
    autoType: false,
  };
}

// Biến toàn cục để theo dõi cửa sổ chính
let mainWindow;

// Tạo cửa sổ chính
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
    icon: path.join(__dirname, "assets/icons/icon.png"),
  });

  // Load giao diện chính
  mainWindow.loadFile("renderer/index.html");

  // Mở DevTools trong chế độ phát triển
  // mainWindow.webContents.openDevTools();
}

// Khởi tạo ứng dụng
app.whenReady().then(() => {
  createWindow();

  // Kiểm tra automation khi app khởi động
  checkAutomationAvailability();

  app.on("activate", function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Thoát ứng dụng khi tắt tất cả cửa sổ
app.on("window-all-closed", function () {
  if (process.platform !== "darwin") app.quit();
});

// Quản lý tài khoản
ipcMain.handle("get-accounts", async () => {
  try {
    const accountsData = fs.readFileSync(
      path.join(__dirname, "accounts.json"),
      "utf8"
    );
    return JSON.parse(accountsData);
  } catch (error) {
    console.error("Lỗi khi đọc danh sách tài khoản:", error);
    return [];
  }
});

ipcMain.handle("save-accounts", async (event, accounts) => {
  try {
    fs.writeFileSync(
      path.join(__dirname, "accounts.json"),
      JSON.stringify(accounts, null, 2),
      "utf8"
    );
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi lưu danh sách tài khoản:", error);
    return { success: false, error: error.message };
  }
});

// Quản lý cấu hình
ipcMain.handle("get-config", async () => {
  return config;
});

ipcMain.handle("save-config", async (event, newConfig) => {
  try {
    config = newConfig;
    fs.writeFileSync(
      path.join(__dirname, "config.json"),
      JSON.stringify(config, null, 2),
      "utf8"
    );

    // Kiểm tra lại automation khi cài đặt được lưu
    checkAutomationAvailability();

    return { success: true };
  } catch (error) {
    console.error("Lỗi khi lưu cấu hình:", error);
    return { success: false, error: error.message };
  }
});

// Khởi chạy game
ipcMain.handle("launch-game", async (event, account) => {
  try {
    const { javaBin, emuJar, gameJar } = config;

    if (!javaBin || !emuJar || !gameJar) {
      return {
        success: false,
        error:
          "Thiếu thông tin cấu hình để khởi chạy game. Vui lòng kiểm tra cài đặt.",
      };
    }

    // Lưu tài khoản hiện tại để tự động đăng nhập
    store.set("currentAccount", account);

    // Khởi chạy MicroEmulator với game JAR
    const emulatorProcess = spawn(javaBin, ["-jar", emuJar, gameJar], {
      detached: true,
    });

    // Gửi F2 để đăng nhập tự động sau khi game khởi chạy
    setTimeout(async () => {
      try {
        await sendKeyWithCustomJava("f2");
      } catch (error) {
        console.error("Lỗi khi gửi F2:", error);
      }
    }, 5000);

    emulatorProcess.unref();

    return { success: true };
  } catch (error) {
    console.error("Lỗi khi khởi chạy game:", error);
    return { success: false, error: error.message };
  }
});

// Xử lý tự động hóa
ipcMain.handle("execute-script", async (event, script) => {
  try {
    // Thực thi script tự động
    // Script sẽ được xử lý bởi module robotjs trong renderer
    mainWindow.webContents.send("execute-automation-script", script);
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi thực thi script:", error);
    return { success: false, error: error.message };
  }
});

// Chọn file JAR
ipcMain.handle("select-jar-file", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openFile"],
    filters: [{ name: "JAR Files", extensions: ["jar"] }],
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// Chọn thư mục
ipcMain.handle("select-directory", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openDirectory"],
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// ===== AUTOMATION IPC HANDLERS =====

// Kiểm tra automation có sẵn không
ipcMain.handle("automation-check-available", async () => {
  // Kiểm tra lại trạng thái automation
  checkAutomationAvailability();

  return {
    available: automationAvailable,
    error: automationError,
  };
});

// Thực hiện hành động automation
ipcMain.handle("automation-execute-action", async (event, action) => {
  if (!automationAvailable) {
    throw new Error("Automation không khả dụng: " + automationError);
  }

  try {
    switch (action.type) {
      case "key":
        await sendKeyWithCustomJava(action.key);
        break;
      case "text":
        await sendTextWithCustomJava(action.text);
        break;
      case "delay":
        // Delay được xử lý ở renderer side
        break;
      default:
        throw new Error(`Loại hành động không được hỗ trợ: ${action.type}`);
    }
    return { success: true };
  } catch (error) {
    console.error("Lỗi khi thực hiện automation action:", error);
    return { success: false, error: error.message };
  }
});

// Lấy thông tin tài khoản hiện tại cho automation
ipcMain.handle("automation-get-current-account", async () => {
  try {
    // Đọc từ file current_account.json nếu có
    const currentAccountPath = path.join(__dirname, "current_account.json");
    if (fs.existsSync(currentAccountPath)) {
      const accountData = fs.readFileSync(currentAccountPath, "utf8");
      return JSON.parse(accountData);
    }
    return null;
  } catch (error) {
    console.error("Lỗi khi lấy thông tin tài khoản hiện tại:", error);
    return null;
  }
});
